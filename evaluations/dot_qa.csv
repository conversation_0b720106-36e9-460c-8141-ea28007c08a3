query,reference,source_file
Quels sont les délais de remédiation pour les vulnérabilités critiques sur une plateforme sensible ?,"Pour une plateforme critique à données sensibles, les vulnérabilités critiques (CVSS 9.5–10.0) doivent être corrigées dans un délai de 36 heures après détection.",UM6P_PROC_Gestion de correction des vulnérabilités V1.2.docx
Quels outils sont utilisés pour détecter les vulnérabilités à l’UM6P ?,"L’UM6P utilise des outils automatisés comme SIEM, EDR, Nessus, et Qualys pour détecter les vulnérabilités sur ses systèmes.",UM6P_PROC_Gestion de correction des vulnérabilités V1.2.docx
Quelles sont les étapes principales du processus de gestion des vulnérabilités ?,"Le processus inclut : détection, qualification et classification, plan d’action, remédiation, vérification et validation.",UM6P_PROC_Gestion de correction des vulnérabilités V1.2.docx
Que se passe-t-il si les délais de correction sont dépassés ?,La DSI peut suspendre la plateforme concernée ou décommissionner l’instance sous 30 jours si les délais ne sont pas respectés.,UM6P_PROC_Gestion de correction des vulnérabilités V1.2.docx
Quel score CVSS est associé à une vulnérabilité élevée ?,Une vulnérabilité est considérée comme élevée si son score CVSS est compris entre 7.0 et 9.4.,UM6P_PROC_Gestion de correction des vulnérabilités V1.2.docx
Qui est responsable du pilotage du processus de gestion des vulnérabilités ?,"C’est l’équipe de Cybersécurité opérationnelle qui pilote le processus, lance les scans et coordonne les plans d’action.",UM6P_PROC_Gestion de correction des vulnérabilités V1.2.docx
Quels types de plateformes doivent être scannées hebdomadairement ?,Les plateformes critiques doivent être scannées chaque semaine ou à chaque changement majeur.,UM6P_PROC_Gestion de correction des vulnérabilités V1.2.docx
Quels sont les déclencheurs d’un incident de sécurité SI ?,"Un incident peut être détecté via SIEM/EDR, signalé par un utilisateur ou remonté par le SOC.",UM6P_ PRCS_GESTION DES INCIDENTS DE SÉCURITÉ SI V2.0.docx
Quels sont les indicateurs de performance suivis dans la gestion des incidents ?,"Délai moyen de résolution, taux d’escalade, nombre de RCA par trimestre, incidents récurrents.",UM6P_ PRCS_GESTION DES INCIDENTS DE SÉCURITÉ SI V2.0.docx
Qui approuve le traitement final d’un incident de sécurité SI ?,L’approbation finale revient au responsable DOT ou à la cellule de crise selon la criticité de l’incident.,UM6P_ PRCS_GESTION DES INCIDENTS DE SÉCURITÉ SI V2.0.docx
Quelles sont les étapes après une infection virale sur un poste de travail ?,"Isolation, notification, sauvegarde, quarantaine, scan, analyse des logs, éradication, redémarrage, patchs, scan final, et mise à jour des politiques.",UM6P_PRCD_gestion_incidents_sécurité_Poste_de_travail_&_Serveurs_v2.0.docx
Que faire lorsqu’un email suspect est détecté ?,"Il faut éviter de cliquer, transférer à <EMAIL>, puis supprimer l’email.",UM6P_PRCD_gestion_incidents_sécurité_Poste_de_travail_&_Serveurs_v2.0.docx
Quels outils sont utilisés pour surveiller les infections ?,"Antivirus, EDR, SIEM, pare-feu, solutions de gestion des vulnérabilités, et outils de monitoring.",UM6P_PRCD_gestion_incidents_sécurité_Poste_de_travail_&_Serveurs_v2.0.docx
Qui est responsable de l’analyse RCA après une infection virale ?,L’équipe de cybersécurité opérationnelle est chargée de l’analyse post-incident.,UM6P_PRCD_gestion_incidents_sécurité_Poste_de_travail_&_Serveurs_v2.0.docx
Quels sont les incidents critiques sur les équipements réseau ?,"Attaque DoS/DDoS, intrusion, échec du NAC, dysfonctionnement firewall, ou malware détecté.",UM6P_PRCD_Gestion_des_incidents de sécurité reseau v1.2.docx
Quelle est la première action lors d’un incident réseau ?,L’équipement concerné est isolé et un ticket est ouvert dans MyServices.,UM6P_PRCD_Gestion_des_incidents de sécurité reseau v1.2.docx
Quels outils de sécurité réseau sont mentionnés dans la procédure ?,"Microsoft Defender, LogPoint (SIEM), ISE, Ucopia (NAC), Palo Alto, Fortigate, F5 ASM (firewalls).",UM6P_PRCD_Gestion_des_incidents de sécurité reseau v1.2.docx
Que contient le rapport d’un incident réseau ?,"Détail de l’incident, mesures prises, analyse des logs, recommandations, communication aux parties concernées.",UM6P_PRCD_Gestion_des_incidents de sécurité reseau v1.2.docx
Quelles informations sont nécessaires pour créer un compte professeur ?,"Prénom, nom, email personnel, poste, type de contrat, date de début, campus, manager, etc.",UM6P_PRCD_Création compte Professeur et Postdoc.pdf
Quel est le délai de création du compte professeur ?,Le compte doit être opérationnel dans les 2 jours suivant la réception complète de la demande RH.,UM6P_PRCD_Création compte Professeur et Postdoc.pdf
Quel suffixe est utilisé pour les postdocs ?,Les comptes postdocs peuvent comporter le suffixe `-EXT` dans l’adresse email.,UM6P_PRCD_Création compte Professeur et Postdoc.pdf
Comment sont créés les comptes étudiants ?,"La scolarité transmet une liste à l’équipe IT, qui crée les comptes AD, assigne la licence Education et active MFA.",UM6P_PRCD_Création des Comptes IT – Étudiants UM6P.pdf
Quel est le délai de création des comptes étudiants ?,Les comptes doivent être prêts au plus tard 3 jours avant le début des cours.,UM6P_PRCD_Création des Comptes IT – Étudiants UM6P.pdf
Que contient la demande RH pour modifier un compte IT ?,"Changement de poste, département, contrat, date d’expiration, groupes d’accès, etc.",UM6P_PRCD_Modification de Compte IT – UM6P.pdf
Qui met à jour les attributs dans AD ?,C’est l’équipe GDI (Gestion des Identités) qui réalise les mises à jour dans Active Directory.,UM6P_PRCD_Modification de Compte IT – UM6P.pdf
Comment se déroule la radiation d’un compte IT ?,"RH notifie IT, le compte est désactivé dans AD, retiré des groupes, masqué, et sa licence est modifiée pour archiver les emails.",UM6P_PRCD_Radiation des Comptes IT – UM6P.pdf
Quel type de licence est appliqué après radiation ?,Une licence Microsoft A1 est attribuée pour conserver l’historique des emails.,UM6P_PRCD_Radiation des Comptes IT – UM6P.pdf
Quelles personnes peuvent avoir un compte externe ?,"Intérimaires, stagiaires, consultants, partenaires industriels ou académiques.",UM6P_PRCD_Création des Comptes Externes – UM6P.pdf
Pourquoi un NDA est-il requis pour les comptes externes ?,Pour garantir la confidentialité et la conformité contractuelle des accès externes.,UM6P_PRCD_Création des Comptes Externes – UM6P.pdf
Quelles sont les étapes de création d’un compte externe ?,"Demande sponsorisée, vérification IT, création AD avec suffixe `-EXT`, MFA et droits restreints.",UM6P_PRCD_Création des Comptes Externes – UM6P.pdf
Comment demander l’installation d’un logiciel ?,"Via l’outil ITSM ; le support vérifie s’il est autorisé, sinon l’équipe Workplace valide ou refuse.",UM6P_PRCD_Procédure_Installation_Logiciel_v1.0.pdf
Que faire si un logiciel demandé est non conforme ?,Il est escaladé à l’équipe Workplace pour vérification de conformité et sécurité.,UM6P_PRCD_Procédure_Installation_Logiciel_v1.0.pdf
Qui met à jour la liste des logiciels autorisés ?,L’équipe Workplace maintient cette liste et supervise les exceptions.,UM6P_PRCD_Procédure_Installation_Logiciel_v1.0.pdf
Quelles étapes sont suivies pour le déploiement d’un laptop ?,"Image standard installée localement, configuration Intune, profil de sécurité appliqué, OOBE pour l’utilisateur.",UM6P_PRCD_Deploy_new_Device_v1.1.pdf
Quelle image est utilisée pour les OPS ?,TS-UM6P-OPS&TA_Build_n_Capture_Reference avec outils pédagogiques et comptes locaux spécifiques.,UM6P_PRCD_Deploy_new_Device_v1.1.pdf
Quels profils sont appliqués via Intune pour les mobiles ?,"Profils de sécurité, conformité, Microsoft Defender, enrôlement via Work Profile.",UM6P_PRCD_Deploy_new_Device_v1.1.pdf
Quelle est la durée totale de la campagne de sensibilisation 2025–2026 ?,La campagne s’étend sur 12 mois avec 5 phases successives.,Operational Security Awareness & Training Plan 2025–2026.pdf
Quel est l’objectif principal de la campagne de sensibilisation ?,Former les utilisateurs à adopter des pratiques numériques sécurisées et favoriser la résilience opérationnelle.,Operational Security Awareness & Training Plan 2025–2026.pdf
Qui sont les parties prenantes de la campagne ?,"CSDM, DOT, HDC, Communication Interne, et les leaders des entités DEO.",Operational Security Awareness & Training Plan 2025–2026.pdf
Quelles thématiques sont couvertes pendant la campagne ?,"Sécurité numérique, gouvernance des données, continuité des systèmes d'information.",Operational Security Awareness & Training Plan 2025–2026.pdf
Qu’est-ce que le réseau des Digital Awareness Ambassadors ?,Un groupe interne d’ambassadeurs formés pour relayer les bonnes pratiques de sécurité dans chaque entité UM6P.,Operational Security Awareness & Training Plan 2025–2026.pdf
Comment tracer toutes les créations de comptes ?,Elles sont journalisées dans le référentiel d’identité et auditées périodiquement.,UM6P_PRCD_Création des Comptes Informatiques – Staff UM6P.pdf
Quel outil est utilisé pour le suivi des tickets IT ?,"L’outil MyServices est utilisé pour toutes les demandes, incidents, installations et modifications.",UM6P_PRCD_gestion_incidents_sécurité_Poste_de_travail_&_Serveurs_v2.0.docx
"Comment sont gérés les accès Wi-Fi, messagerie et intranet ?",Via des groupes assignés dans AD au moment de la création du compte.,UM6P_PRCD_Création des Comptes Informatiques – Staff UM6P.pdf
Quelle politique de mot de passe est appliquée ?,Une politique de complexité UM6P et l’activation obligatoire de l’authentification multifactorielle (MFA).,UM6P_PRCD_Création des Comptes Informatiques – Staff UM6P.pdf
Quand un ticket peut-il être clôturé après installation logicielle ?,Lorsqu’un logiciel est installé avec succès et validé selon les normes de sécurité IT.,UM6P_PRCD_Procédure_Installation_Logiciel_v1.0.pdf
Que se passe-t-il si un incident impacte un service critique ?,Une procédure d’escalade formelle est enclenchée avec notification au RSSI et à la DOT.,UM6P_PRCD_Gestion_des_incidents de sécurité reseau v1.2.docx
Quels logs sont analysés lors d’un incident réseau ?,"Journaux du SIEM, WLC, firewall, DNS, DHCP, CLI et autres systèmes critiques.",UM6P_PRCD_Gestion_des_incidents de sécurité reseau v1.2.docx
Quelle est la différence entre un incident SI et un incident poste de travail ?,"Un incident SI touche l’infrastructure globale, tandis qu’un incident poste de travail concerne une machine individuelle ou un serveur isolé.",UM6P_ PRCS_GESTION DES INCIDENTS DE SÉCURITÉ SI V2.0.docx / UM6P_PRCD_gestion_incidents_sécurité_Poste_de_travail_&_Serveurs_v2.0.docx
Pourquoi le suivi des KPI est-il important dans la sécurité ?,"Il permet de mesurer l’efficacité des processus, identifier les lacunes récurrentes, et améliorer la prévention des incidents.",UM6P_ PRCS_GESTION DES INCIDENTS DE SÉCURITÉ SI V2.0.docx
