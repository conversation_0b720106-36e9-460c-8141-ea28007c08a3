'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Send, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface MessageInputProps {
  onSendMessage: (message: string) => void
  disabled?: boolean
  isTyping?: boolean
}

export function MessageInput({ onSendMessage, disabled = false, isTyping = false }: MessageInputProps) {
  const [message, setMessage] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!message.trim() || disabled || isTyping) {
      return
    }

    onSendMessage(message.trim())
    setMessage('')
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  useEffect(() => {
    // Focus input when component mounts
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  const isDisabled = disabled || isTyping || !message.trim()

  return (
    <div className="p-6">
      <form onSubmit={handleSubmit} className="flex gap-3">
        <div className="flex-1">
          <Input
            ref={inputRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={
              disabled
                ? "Connecting to RAG DOT..."
                : isTyping
                  ? "RAG DOT is thinking..."
                  : "Ask me anything about DOT policies and procedures..."
            }
            disabled={disabled}
            className="resize-none h-12 text-base shadow-soft border-slate-200 focus:border-blue-300 focus:ring-blue-100 rounded-xl"
          />
        </div>
        <Button
          type="submit"
          size="icon"
          disabled={isDisabled}
          className={cn(
            "flex-shrink-0 h-12 w-12 rounded-xl shadow-soft hover-lift",
            "bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700",
            isDisabled && "opacity-50 cursor-not-allowed hover:transform-none"
          )}
        >
          {isTyping ? (
            <Loader2 className="w-5 h-5 animate-spin" />
          ) : (
            <Send className="w-5 h-5" />
          )}
        </Button>
      </form>
    </div>
  )
}
