'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { Wifi, WifiOff, Loader2, AlertTriangle } from 'lucide-react'

interface ConnectionStatusProps {
  status: 'connecting' | 'connected' | 'disconnected' | 'error'
  className?: string
}

export function ConnectionStatus({ status, className }: ConnectionStatusProps) {
  const getStatusConfig = () => {
    switch (status) {
      case 'connecting':
        return {
          icon: <Loader2 className="w-4 h-4 animate-spin" />,
          text: 'Connecting...',
          color: 'text-yellow-600'
        }
      case 'connected':
        return {
          icon: <Wifi className="w-4 h-4" />,
          text: 'Connected',
          color: 'text-green-600'
        }
      case 'disconnected':
        return {
          icon: <WifiOff className="w-4 h-4" />,
          text: 'Disconnected',
          color: 'text-muted-foreground'
        }
      case 'error':
        return {
          icon: <AlertTriangle className="w-4 h-4" />,
          text: 'Connection Error',
          color: 'text-destructive'
        }
      default:
        return {
          icon: <WifiOff className="w-4 h-4" />,
          text: 'Unknown',
          color: 'text-muted-foreground'
        }
    }
  }

  const config = getStatusConfig()

  return (
    <div className={cn(
      "flex items-center gap-2 text-sm",
      config.color,
      className
    )}>
      {config.icon}
      <span>{config.text}</span>
    </div>
  )
}
