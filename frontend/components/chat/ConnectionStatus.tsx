'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { Wifi, WifiOff, Loader2, AlertTriangle } from 'lucide-react'

interface ConnectionStatusProps {
  status: 'connecting' | 'connected' | 'disconnected' | 'error'
  className?: string
}

export function ConnectionStatus({ status, className }: ConnectionStatusProps) {
  const getStatusConfig = () => {
    switch (status) {
      case 'connecting':
        return {
          icon: <Loader2 className="w-4 h-4 animate-spin" />,
          text: 'Connecting...',
          color: 'text-amber-600',
          bgColor: 'bg-amber-50',
          borderColor: 'border-amber-200'
        }
      case 'connected':
        return {
          icon: <Wifi className="w-4 h-4" />,
          text: 'Connected',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200'
        }
      case 'disconnected':
        return {
          icon: <WifiOff className="w-4 h-4" />,
          text: 'Disconnected',
          color: 'text-slate-500',
          bgColor: 'bg-slate-50',
          borderColor: 'border-slate-200'
        }
      case 'error':
        return {
          icon: <AlertTriangle className="w-4 h-4" />,
          text: 'Connection Error',
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200'
        }
      default:
        return {
          icon: <WifiOff className="w-4 h-4" />,
          text: 'Unknown',
          color: 'text-slate-500',
          bgColor: 'bg-slate-50',
          borderColor: 'border-slate-200'
        }
    }
  }

  const config = getStatusConfig()

  return (
    <div className={cn(
      "flex items-center gap-2 text-sm px-3 py-1.5 rounded-full border shadow-soft",
      config.color,
      config.bgColor,
      config.borderColor,
      className
    )}>
      {config.icon}
      <span className="font-medium">{config.text}</span>
    </div>
  )
}
