'use client'

import React from 'react'
import { Message } from '@/types/chat'
import { formatTimestamp, cn } from '@/lib/utils'
import { Card, CardContent } from '@/components/ui/card'
import { User, Bot, AlertCircle, Info } from 'lucide-react'

interface MessageBubbleProps {
  message: Message
}

export function MessageBubble({ message }: MessageBubbleProps) {
  const isUser = message.type === 'user'
  const isSystem = message.type === 'system'
  const isError = message.type === 'error'

  const getIcon = () => {
    switch (message.type) {
      case 'user':
        return <User className="w-4 h-4" />
      case 'assistant':
        return <Bot className="w-4 h-4" />
      case 'error':
        return <AlertCircle className="w-4 h-4" />
      case 'system':
        return <Info className="w-4 h-4" />
      default:
        return null
    }
  }

  const getBubbleStyles = () => {
    if (isUser) {
      return "bg-gradient-to-r from-blue-500 to-blue-600 text-white ml-auto shadow-medium hover-lift"
    }
    if (isError) {
      return "bg-gradient-to-r from-red-500 to-red-600 text-white shadow-medium"
    }
    if (isSystem) {
      return "bg-gradient-to-r from-slate-100 to-slate-200 text-slate-700 mx-auto text-center shadow-soft"
    }
    return "bg-white text-slate-700 shadow-medium border border-slate-100 hover-lift"
  }

  return (
    <div className={cn(
      "flex w-full mb-4",
      isUser ? "justify-end" : "justify-start",
      isSystem ? "justify-center" : ""
    )}>
      <div className={cn(
        "max-w-[80%] min-w-[100px]",
        isSystem ? "max-w-[60%]" : ""
      )}>
        <Card className={cn("border-0 rounded-2xl", getBubbleStyles())}>
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 mt-1">
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center",
                  isUser ? "bg-white/20" : "bg-blue-100"
                )}>
                  {getIcon()}
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm whitespace-pre-wrap break-words leading-relaxed">
                  {message.content}
                </div>
                
                {/* Sources section for assistant messages */}
                {message.sources && message.sources.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-slate-200">
                    <div className="text-xs font-semibold mb-3 text-slate-600 flex items-center gap-2">
                      📚 Sources & References
                    </div>
                    <div className="space-y-3">
                      {message.sources.map((source, index) => (
                        <div key={index} className="text-xs bg-slate-50 rounded-xl p-3 border border-slate-100">
                          {source.title && (
                            <div className="font-semibold mb-2 text-slate-700">{source.title}</div>
                          )}
                          <div className="line-clamp-3 text-slate-600 leading-relaxed">{source.content}</div>
                          {source.score && (
                            <div className="text-xs mt-2 text-slate-500 flex items-center gap-1">
                              <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                              Relevance: {(source.score * 100).toFixed(1)}%
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <div className={cn(
              "text-xs mt-3 flex items-center gap-1",
              isUser ? "justify-end text-white/70" : "justify-start text-slate-500"
            )}>
              <span>🕒</span>
              {formatTimestamp(message.timestamp)}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
