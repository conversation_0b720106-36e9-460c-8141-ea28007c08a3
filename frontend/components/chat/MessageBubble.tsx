'use client'

import React from 'react'
import { Message } from '@/types/chat'
import { formatTimestamp, cn } from '@/lib/utils'
import { Card, CardContent } from '@/components/ui/card'
import { User, Bot, AlertCircle, Info } from 'lucide-react'

interface MessageBubbleProps {
  message: Message
}

export function MessageBubble({ message }: MessageBubbleProps) {
  const isUser = message.type === 'user'
  const isSystem = message.type === 'system'
  const isError = message.type === 'error'

  const getIcon = () => {
    switch (message.type) {
      case 'user':
        return <User className="w-4 h-4" />
      case 'assistant':
        return <Bot className="w-4 h-4" />
      case 'error':
        return <AlertCircle className="w-4 h-4" />
      case 'system':
        return <Info className="w-4 h-4" />
      default:
        return null
    }
  }

  const getBubbleStyles = () => {
    if (isUser) {
      return "bg-primary text-primary-foreground ml-auto"
    }
    if (isError) {
      return "bg-destructive text-destructive-foreground"
    }
    if (isSystem) {
      return "bg-muted text-muted-foreground mx-auto text-center"
    }
    return "bg-card text-card-foreground border"
  }

  return (
    <div className={cn(
      "flex w-full mb-4",
      isUser ? "justify-end" : "justify-start",
      isSystem ? "justify-center" : ""
    )}>
      <div className={cn(
        "max-w-[80%] min-w-[100px]",
        isSystem ? "max-w-[60%]" : ""
      )}>
        <Card className={cn("rounded-lg", getBubbleStyles())}>
          <CardContent className="p-3">
            <div className="flex items-start gap-2">
              <div className="flex-shrink-0 mt-0.5">
                {getIcon()}
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm whitespace-pre-wrap break-words">
                  {message.content}
                </div>
                
                {/* Sources section for assistant messages */}
                {message.sources && message.sources.length > 0 && (
                  <div className="mt-3 pt-3 border-t border-border/50">
                    <div className="text-xs font-medium mb-2 text-muted-foreground">
                      Sources:
                    </div>
                    <div className="space-y-2">
                      {message.sources.map((source, index) => (
                        <div key={index} className="text-xs bg-muted/50 rounded p-2 border">
                          {source.title && (
                            <div className="font-medium mb-1">{source.title}</div>
                          )}
                          <div className="line-clamp-3 text-muted-foreground">{source.content}</div>
                          {source.score && (
                            <div className="text-xs mt-1 text-muted-foreground">
                              Relevance: {(source.score * 100).toFixed(1)}%
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <div className={cn(
              "text-xs mt-2 opacity-60",
              isUser ? "text-right" : "text-left"
            )}>
              {formatTimestamp(message.timestamp)}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
