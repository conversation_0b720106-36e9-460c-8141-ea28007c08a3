'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Bot } from 'lucide-react'

export function TypingIndicator() {
  return (
    <div className="flex w-full mb-4 justify-start">
      <div className="max-w-[80%]">
        <Card className="bg-card text-card-foreground border rounded-lg">
          <CardContent className="p-3">
            <div className="flex items-center gap-2">
              <Bot className="w-4 h-4 flex-shrink-0" />
              <div className="flex items-center space-x-1">
                <div className="text-sm">RAG DOT is thinking</div>
                <div className="flex space-x-1 ml-2">
                  <div className="w-2 h-2 bg-current rounded-full typing-indicator" style={{ animationDelay: '0ms' }}></div>
                  <div className="w-2 h-2 bg-current rounded-full typing-indicator" style={{ animationDelay: '150ms' }}></div>
                  <div className="w-2 h-2 bg-current rounded-full typing-indicator" style={{ animationDelay: '300ms' }}></div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
