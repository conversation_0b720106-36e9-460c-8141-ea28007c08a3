'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Bot } from 'lucide-react'

export function TypingIndicator() {
  return (
    <div className="flex w-full mb-4 justify-start">
      <div className="max-w-[80%]">
        <Card className="bg-white text-slate-700 shadow-medium border border-slate-100 hover-lift rounded-2xl">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                <Bot className="w-4 h-4 flex-shrink-0 text-blue-600" />
              </div>
              <div className="flex items-center space-x-2">
                <div className="text-sm font-medium text-slate-600">RAG DOT is thinking</div>
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-blue-400 rounded-full typing-indicator" style={{ animationDelay: '0ms' }}></div>
                  <div className="w-2 h-2 bg-blue-400 rounded-full typing-indicator" style={{ animationDelay: '150ms' }}></div>
                  <div className="w-2 h-2 bg-blue-400 rounded-full typing-indicator" style={{ animationDelay: '300ms' }}></div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
