'use client'

import React, { useEffect, useRef } from 'react'
import { useWebSocket } from '@/hooks/useWebSocket'
import { MessageBubble } from './MessageBubble'
import { TypingIndicator } from './TypingIndicator'
import { MessageInput } from './MessageInput'
import { ConnectionStatus } from './ConnectionStatus'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Trash2, RefreshCw } from 'lucide-react'
import { cn } from '@/lib/utils'

export function ChatInterface() {
  const {
    messages,
    isConnected,
    isTyping,
    connectionStatus,
    sendMessage,
    clearMessages,
    connect
  } = useWebSocket()

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [messages, isTyping])

  const handleSendMessage = (message: string) => {
    if (isConnected && message.trim()) {
      sendMessage(message)
    }
  }

  const handleClearMessages = () => {
    clearMessages()
  }

  const handleReconnect = () => {
    connect()
  }

  return (
    <div className="flex flex-col h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <Card className="rounded-none border-x-0 border-t-0 shadow-medium bg-white/80 backdrop-blur-sm">
        <CardHeader className="pb-4 pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-xl gradient-bg flex items-center justify-center shadow-soft">
                <span className="text-2xl font-bold text-white">🎯</span>
              </div>
              <div>
                <CardTitle className="text-2xl font-bold gradient-text">RAG DOT</CardTitle>
                <p className="text-sm text-muted-foreground mt-1 font-medium">
                  Department of Operations and Technology
                </p>
                <p className="text-xs text-muted-foreground">
                  AI-powered assistant for policies and procedures
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <ConnectionStatus status={connectionStatus} />
              <div className="flex gap-2">
                {connectionStatus === 'error' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleReconnect}
                    className="gap-2 hover-lift shadow-soft border-blue-200 hover:border-blue-300"
                  >
                    <RefreshCw className="w-4 h-4" />
                    Reconnect
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearMessages}
                  disabled={messages.length === 0}
                  className="gap-2 hover-lift shadow-soft border-slate-200 hover:border-slate-300"
                >
                  <Trash2 className="w-4 h-4" />
                  Clear
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Messages Container */}
      <div className="flex-1 overflow-hidden">
        <div
          ref={messagesContainerRef}
          className="h-full overflow-y-auto scrollbar-thin px-4 py-4"
        >
          {messages.length === 0 && (
            <div className="flex items-center justify-center h-full p-8">
              <div className="text-center max-w-2xl">
                <div className="w-24 h-24 mx-auto mb-6 rounded-2xl gradient-bg flex items-center justify-center shadow-medium">
                  <span className="text-4xl">🎯</span>
                </div>
                <h3 className="text-3xl font-bold mb-3 gradient-text">Welcome to RAG DOT</h3>
                <p className="text-xl text-slate-600 mb-2 font-medium">Department of Operations and Technology</p>
                <p className="text-muted-foreground mb-8 text-lg">
                  Your intelligent assistant for UM6P policies, procedures, and operational guidelines.
                </p>
                <div className="grid md:grid-cols-2 gap-4 text-sm">
                  <Card className="p-4 hover-lift shadow-soft border-blue-100">
                    <h4 className="font-semibold text-blue-700 mb-2">🔐 Security & Access</h4>
                    <ul className="text-left space-y-1 text-muted-foreground">
                      <li>• Account creation and management</li>
                      <li>• Security policies and procedures</li>
                      <li>• Access control guidelines</li>
                    </ul>
                  </Card>
                  <Card className="p-4 hover-lift shadow-soft border-green-100">
                    <h4 className="font-semibold text-green-700 mb-2">⚙️ Operations & IT</h4>
                    <ul className="text-left space-y-1 text-muted-foreground">
                      <li>• IT incident management</li>
                      <li>• Software installation guidelines</li>
                      <li>• Operational procedures</li>
                    </ul>
                  </Card>
                </div>
                <p className="text-sm text-muted-foreground mt-6 italic">
                  Ask me anything about UM6P policies and I'll provide detailed, accurate information.
                </p>
              </div>
            </div>
          )}

          {messages.map((message) => (
            <MessageBubble key={message.id} message={message} />
          ))}

          {isTyping && <TypingIndicator />}
          
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input Area */}
      <div className="bg-white/80 backdrop-blur-sm border-t border-slate-200 shadow-medium">
        <MessageInput
          onSendMessage={handleSendMessage}
          disabled={!isConnected}
          isTyping={isTyping}
        />
      </div>
    </div>
  )
}
