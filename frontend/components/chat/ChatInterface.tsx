'use client'

import React, { useEffect, useRef } from 'react'
import { useWebSocket } from '@/hooks/useWebSocket'
import { MessageBubble } from './MessageBubble'
import { TypingIndicator } from './TypingIndicator'
import { MessageInput } from './MessageInput'
import { ConnectionStatus } from './ConnectionStatus'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Trash2, RefreshCw } from 'lucide-react'
import { cn } from '@/lib/utils'

export function ChatInterface() {
  const {
    messages,
    isConnected,
    isTyping,
    connectionStatus,
    sendMessage,
    clearMessages,
    connect
  } = useWebSocket()

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [messages, isTyping])

  const handleSendMessage = (message: string) => {
    if (isConnected && message.trim()) {
      sendMessage(message)
    }
  }

  const handleClearMessages = () => {
    clearMessages()
  }

  const handleReconnect = () => {
    connect()
  }

  return (
    <div className="flex flex-col h-screen bg-background">
      {/* Header */}
      <Card className="rounded-none border-x-0 border-t-0 border-b">
        <CardHeader className="pb-4 pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 rounded-lg simple-bg flex items-center justify-center">
                <span className="text-lg text-white">🎯</span>
              </div>
              <div>
                <CardTitle className="text-xl font-semibold simple-text">RAG DOT</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Department of Operations and Technology
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <ConnectionStatus status={connectionStatus} />
              <div className="flex gap-2">
                {connectionStatus === 'error' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleReconnect}
                    className="gap-2"
                  >
                    <RefreshCw className="w-4 h-4" />
                    Reconnect
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearMessages}
                  disabled={messages.length === 0}
                  className="gap-2"
                >
                  <Trash2 className="w-4 h-4" />
                  Clear
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Messages Container */}
      <div className="flex-1 overflow-hidden">
        <div
          ref={messagesContainerRef}
          className="h-full overflow-y-auto scrollbar-thin px-4 py-4"
        >
          {messages.length === 0 && (
            <div className="flex items-center justify-center h-full p-8">
              <div className="text-center max-w-lg">
                <div className="w-16 h-16 mx-auto mb-6 rounded-lg simple-bg flex items-center justify-center">
                  <span className="text-2xl">🎯</span>
                </div>
                <h3 className="text-2xl font-semibold mb-2 simple-text">Welcome to RAG DOT</h3>
                <p className="text-muted-foreground mb-6">
                  Department of Operations and Technology
                </p>
                <p className="text-muted-foreground mb-8">
                  Your intelligent assistant for UM6P policies, procedures, and operational guidelines.
                </p>
                <div className="space-y-3 text-sm text-left">
                  <div className="p-3 border rounded-lg">
                    <h4 className="font-medium mb-2">Security & Access</h4>
                    <p className="text-muted-foreground text-xs">Account management, security policies, access control</p>
                  </div>
                  <div className="p-3 border rounded-lg">
                    <h4 className="font-medium mb-2">Operations & IT</h4>
                    <p className="text-muted-foreground text-xs">Incident management, software guidelines, procedures</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {messages.map((message) => (
            <MessageBubble key={message.id} message={message} />
          ))}

          {isTyping && <TypingIndicator />}
          
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input Area */}
      <div className="border-t">
        <MessageInput
          onSendMessage={handleSendMessage}
          disabled={!isConnected}
          isTyping={isTyping}
        />
      </div>
    </div>
  )
}
