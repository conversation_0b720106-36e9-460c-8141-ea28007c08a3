# RAG DOT - Department of Operations and Technology

A modern Next.js frontend interface for RAG DOT (Department of Operations and Technology), featuring a sleek GPT-like chat interface that connects to the WebSocket RAG server.

## Features

- 🎨 Modern, responsive chat interface
- 🔄 Real-time WebSocket communication
- 💬 GPT-like conversation experience
- 📱 Mobile-friendly design
- 🔗 Source citations and metadata display
- 🔄 Auto-reconnection on connection loss
- ⚡ TypeScript for type safety
- 🎯 Tailwind CSS for styling

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm, yarn, or pnpm
- Running WebSocket server (websocket_server.py)

### Installation

1. Navigate to the frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Configure environment variables:
```bash
cp .env.local.example .env.local
```

Edit `.env.local` to match your WebSocket server configuration:
```env
NEXT_PUBLIC_WEBSOCKET_URL=ws://localhost:8000
```

4. Start the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Usage

1. Make sure your WebSocket server is running:
```bash
python websocket_server.py
```

2. Open the frontend in your browser
3. Start chatting with RAG DOT!

## Project Structure

```
frontend/
├── app/                    # Next.js app directory
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── chat/             # Chat-specific components
│   │   ├── ChatInterface.tsx
│   │   ├── MessageBubble.tsx
│   │   ├── MessageInput.tsx
│   │   ├── TypingIndicator.tsx
│   │   └── ConnectionStatus.tsx
│   └── ui/               # Reusable UI components
│       ├── button.tsx
│       ├── card.tsx
│       └── input.tsx
├── hooks/                # Custom React hooks
│   └── useWebSocket.ts   # WebSocket management
├── lib/                  # Utility functions
│   └── utils.ts
├── types/                # TypeScript type definitions
│   └── chat.ts
└── ...config files
```

## Features in Detail

### Chat Interface
- Clean, modern design inspired by ChatGPT
- Message bubbles with proper styling for user/assistant messages
- Typing indicators when the assistant is processing
- Source citations displayed with assistant responses

### WebSocket Integration
- Automatic connection management
- Reconnection logic with exponential backoff
- Real-time message handling
- Connection status indicators

### Responsive Design
- Mobile-first approach
- Adaptive layout for different screen sizes
- Touch-friendly interface

## Configuration

### Environment Variables

- `NEXT_PUBLIC_WEBSOCKET_URL`: WebSocket server URL (default: ws://localhost:8000)

### Customization

The interface can be customized by modifying:
- Colors and themes in `tailwind.config.js` and `globals.css`
- Component styling in individual component files
- WebSocket behavior in `hooks/useWebSocket.ts`

## Building for Production

```bash
npm run build
npm start
```

## Contributing

1. Follow the existing code style
2. Use TypeScript for type safety
3. Test your changes thoroughly
4. Update documentation as needed

## Troubleshooting

### Connection Issues
- Ensure the WebSocket server is running
- Check the WebSocket URL in `.env.local`
- Verify CORS settings on the server

### Build Issues
- Clear node_modules and reinstall dependencies
- Check for TypeScript errors
- Ensure all required environment variables are set
