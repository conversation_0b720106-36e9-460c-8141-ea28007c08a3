@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 255 255 255%;
    --foreground: 15 23 42%;
    --card: 255 255 255%;
    --card-foreground: 15 23 42%;
    --popover: 255 255 255%;
    --popover-foreground: 15 23 42%;
    --primary: 15 23 42%;
    --primary-foreground: 255 255 255%;
    --secondary: 248 250 252%;
    --secondary-foreground: 51 65 85%;
    --muted: 248 250 252%;
    --muted-foreground: 100 116 139%;
    --accent: 248 250 252%;
    --accent-foreground: 15 23 42%;
    --destructive: 220 38 38%;
    --destructive-foreground: 255 255 255%;
    --border: 226 232 240%;
    --input: 226 232 240%;
    --ring: 15 23 42%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 15 23 42%;
    --foreground: 248 250 252%;
    --card: 30 41 59%;
    --card-foreground: 248 250 252%;
    --popover: 30 41 59%;
    --popover-foreground: 248 250 252%;
    --primary: 248 250 252%;
    --primary-foreground: 15 23 42%;
    --secondary: 51 65 85%;
    --secondary-foreground: 248 250 252%;
    --muted: 51 65 85%;
    --muted-foreground: 148 163 184%;
    --accent: 51 65 85%;
    --accent-foreground: 248 250 252%;
    --destructive: 220 38 38%;
    --destructive-foreground: 248 250 252%;
    --border: 51 65 85%;
    --input: 51 65 85%;
    --ring: 248 250 252%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgb(156 163 175) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgb(156 163 175);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgb(107 114 128);
}

/* Typing animation */
@keyframes typing {
  0% { opacity: 0.3; }
  50% { opacity: 1; }
  100% { opacity: 0.3; }
}

.typing-indicator {
  animation: typing 1.5s ease-in-out infinite;
}

/* Simple backgrounds */
.simple-bg {
  background: hsl(var(--primary));
}

.simple-text {
  color: hsl(var(--primary));
}

/* Smooth transitions */
* {
  transition: all 0.2s ease-in-out;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Enhanced shadows */
.shadow-soft {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-medium {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
