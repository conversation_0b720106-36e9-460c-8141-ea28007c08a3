"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/chat/ChatInterface.tsx":
/*!*******************************************!*\
  !*** ./components/chat/ChatInterface.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: function() { return /* binding */ ChatInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useWebSocket */ \"(app-pages-browser)/./hooks/useWebSocket.ts\");\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MessageBubble */ \"(app-pages-browser)/./components/chat/MessageBubble.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TypingIndicator */ \"(app-pages-browser)/./components/chat/TypingIndicator.tsx\");\n/* harmony import */ var _MessageInput__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MessageInput */ \"(app-pages-browser)/./components/chat/MessageInput.tsx\");\n/* harmony import */ var _ConnectionStatus__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ConnectionStatus */ \"(app-pages-browser)/./components/chat/ConnectionStatus.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ChatInterface() {\n    _s();\n    const { messages, isConnected, isTyping, connectionStatus, sendMessage, clearMessages, connect } = (0,_hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_2__.useWebSocket)();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (messagesEndRef.current) {\n            messagesEndRef.current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    }, [\n        messages,\n        isTyping\n    ]);\n    const handleSendMessage = (message)=>{\n        if (isConnected && message.trim()) {\n            sendMessage(message);\n        }\n    };\n    const handleClearMessages = ()=>{\n        clearMessages();\n    };\n    const handleReconnect = ()=>{\n        connect();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                className: \"rounded-none border-x-0 border-t-0 border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                    className: \"pb-4 pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 rounded-lg simple-bg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg text-white\",\n                                            children: \"\\uD83C\\uDFAF\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                className: \"text-xl font-semibold simple-text\",\n                                                children: \"RAG DOT\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Department of Operations and Technology\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConnectionStatus__WEBPACK_IMPORTED_MODULE_6__.ConnectionStatus, {\n                                        status: connectionStatus\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            connectionStatus === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleReconnect,\n                                                className: \"gap-2 hover-lift shadow-soft border-blue-200 hover:border-blue-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Reconnect\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleClearMessages,\n                                                disabled: messages.length === 0,\n                                                className: \"gap-2 hover-lift shadow-soft border-slate-200 hover:border-slate-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 87,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Clear\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: messagesContainerRef,\n                    className: \"h-full overflow-y-auto scrollbar-thin px-4 py-4\",\n                    children: [\n                        messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center max-w-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24 h-24 mx-auto mb-6 rounded-2xl gradient-bg flex items-center justify-center shadow-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-4xl\",\n                                            children: \"\\uD83C\\uDFAF\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold mb-3 gradient-text\",\n                                        children: \"Welcome to RAG DOT\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-slate-600 mb-2 font-medium\",\n                                        children: \"Department of Operations and Technology\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground mb-8 text-lg\",\n                                        children: \"Your intelligent assistant for UM6P policies, procedures, and operational guidelines.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"p-4 hover-lift shadow-soft border-blue-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-blue-700 mb-2\",\n                                                        children: \"\\uD83D\\uDD10 Security & Access\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-left space-y-1 text-muted-foreground\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Account creation and management\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Security policies and procedures\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Access control guidelines\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                                lineNumber: 119,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                                className: \"p-4 hover-lift shadow-soft border-green-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-green-700 mb-2\",\n                                                        children: \"⚙️ Operations & IT\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-left space-y-1 text-muted-foreground\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• IT incident management\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Software installation guidelines\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Operational procedures\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                                lineNumber: 127,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground mt-6 italic\",\n                                        children: \"Ask me anything about UM6P policies and I'll provide detailed, accurate information.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this),\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_3__.MessageBubble, {\n                                message: message\n                            }, message.id, false, {\n                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this)),\n                        isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__.TypingIndicator, {}, void 0, false, {\n                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 24\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/80 backdrop-blur-sm border-t border-slate-200 shadow-medium\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageInput__WEBPACK_IMPORTED_MODULE_5__.MessageInput, {\n                    onSendMessage: handleSendMessage,\n                    disabled: !isConnected,\n                    isTyping: isTyping\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"FrO6kQElr1DFvgwh/zKhAI1V2Tw=\", false, function() {\n    return [\n        _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_2__.useWebSocket\n    ];\n});\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat/ChatInterface.tsx\n"));

/***/ })

});