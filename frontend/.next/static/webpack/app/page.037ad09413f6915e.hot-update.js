"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/chat/ChatInterface.tsx":
/*!*******************************************!*\
  !*** ./components/chat/ChatInterface.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: function() { return /* binding */ ChatInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useWebSocket */ \"(app-pages-browser)/./hooks/useWebSocket.ts\");\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MessageBubble */ \"(app-pages-browser)/./components/chat/MessageBubble.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TypingIndicator */ \"(app-pages-browser)/./components/chat/TypingIndicator.tsx\");\n/* harmony import */ var _MessageInput__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MessageInput */ \"(app-pages-browser)/./components/chat/MessageInput.tsx\");\n/* harmony import */ var _ConnectionStatus__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ConnectionStatus */ \"(app-pages-browser)/./components/chat/ConnectionStatus.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ChatInterface() {\n    _s();\n    const { messages, isConnected, isTyping, connectionStatus, sendMessage, clearMessages, connect } = (0,_hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_2__.useWebSocket)();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (messagesEndRef.current) {\n            messagesEndRef.current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    }, [\n        messages,\n        isTyping\n    ]);\n    const handleSendMessage = (message)=>{\n        if (isConnected && message.trim()) {\n            sendMessage(message);\n        }\n    };\n    const handleClearMessages = ()=>{\n        clearMessages();\n    };\n    const handleReconnect = ()=>{\n        connect();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                className: \"rounded-none border-x-0 border-t-0 border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                    className: \"pb-4 pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 rounded-lg simple-bg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg text-white\",\n                                            children: \"\\uD83C\\uDFAF\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                className: \"text-xl font-semibold simple-text\",\n                                                children: \"RAG DOT\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Department of Operations and Technology\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConnectionStatus__WEBPACK_IMPORTED_MODULE_6__.ConnectionStatus, {\n                                        status: connectionStatus\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            connectionStatus === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleReconnect,\n                                                className: \"gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Reconnect\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleClearMessages,\n                                                disabled: messages.length === 0,\n                                                className: \"gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 87,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Clear\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: messagesContainerRef,\n                    className: \"h-full overflow-y-auto scrollbar-thin px-4 py-4\",\n                    children: [\n                        messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center max-w-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 mx-auto mb-6 rounded-lg simple-bg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83C\\uDFAF\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-semibold mb-2 simple-text\",\n                                        children: \"Welcome to RAG DOT\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground mb-6\",\n                                        children: \"Department of Operations and Technology\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground mb-8\",\n                                        children: \"Your intelligent assistant for UM6P policies, procedures, and operational guidelines.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-sm text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-2\",\n                                                        children: \"Security & Access\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground text-xs\",\n                                                        children: \"Account management, security policies, access control\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-2\",\n                                                        children: \"Operations & IT\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground text-xs\",\n                                                        children: \"Incident management, software guidelines, procedures\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this),\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_3__.MessageBubble, {\n                                message: message\n                            }, message.id, false, {\n                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this)),\n                        isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__.TypingIndicator, {}, void 0, false, {\n                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 24\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageInput__WEBPACK_IMPORTED_MODULE_5__.MessageInput, {\n                    onSendMessage: handleSendMessage,\n                    disabled: !isConnected,\n                    isTyping: isTyping\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"FrO6kQElr1DFvgwh/zKhAI1V2Tw=\", false, function() {\n    return [\n        _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_2__.useWebSocket\n    ];\n});\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat/ChatInterface.tsx\n"));

/***/ })

});