"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/chat/MessageInput.tsx":
/*!******************************************!*\
  !*** ./components/chat/MessageInput.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageInput: function() { return /* binding */ MessageInput; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MessageInput auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction MessageInput(param) {\n    let { onSendMessage, disabled = false, isTyping = false } = param;\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!message.trim() || disabled || isTyping) {\n            return;\n        }\n        onSendMessage(message.trim());\n        setMessage(\"\");\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Focus input when component mounts\n        if (inputRef.current) {\n            inputRef.current.focus();\n        }\n    }, []);\n    const isDisabled = disabled || isTyping || !message.trim();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"flex gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                        ref: inputRef,\n                        value: message,\n                        onChange: (e)=>setMessage(e.target.value),\n                        onKeyDown: handleKeyDown,\n                        placeholder: disabled ? \"Connecting to RAG DOT...\" : isTyping ? \"RAG DOT is thinking...\" : \"Ask me anything about DOT policies and procedures...\",\n                        disabled: disabled,\n                        className: \"resize-none h-12 text-base shadow-soft border-slate-200 focus:border-blue-300 focus:ring-blue-100 rounded-xl\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageInput.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageInput.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    type: \"submit\",\n                    size: \"icon\",\n                    disabled: isDisabled,\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex-shrink-0 h-12 w-12 rounded-xl shadow-soft hover-lift\", \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700\", isDisabled && \"opacity-50 cursor-not-allowed hover:transform-none\"),\n                    children: isTyping ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-5 h-5 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageInput.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-5 h-5\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageInput.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageInput.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageInput.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageInput.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_s(MessageInput, \"WHWdSxrJP5+67PoqSh0DpipZuFs=\");\n_c = MessageInput;\nvar _c;\n$RefreshReg$(_c, \"MessageInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat/MessageInput.tsx\n"));

/***/ })

});