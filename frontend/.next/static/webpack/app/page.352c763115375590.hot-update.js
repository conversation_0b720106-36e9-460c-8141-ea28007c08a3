"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/chat/ConnectionStatus.tsx":
/*!**********************************************!*\
  !*** ./components/chat/ConnectionStatus.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectionStatus: function() { return /* binding */ ConnectionStatus; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* __next_internal_client_entry_do_not_use__ ConnectionStatus auto */ \n\n\n\nfunction ConnectionStatus(param) {\n    let { status, className } = param;\n    const getStatusConfig = ()=>{\n        switch(status){\n            case \"connecting\":\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 17\n                    }, this),\n                    text: \"Connecting...\",\n                    color: \"text-yellow-600\"\n                };\n            case \"connected\":\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 17\n                    }, this),\n                    text: \"Connected\",\n                    color: \"text-green-600\"\n                };\n            case \"disconnected\":\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 17\n                    }, this),\n                    text: \"Disconnected\",\n                    color: \"text-muted-foreground\"\n                };\n            case \"error\":\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 17\n                    }, this),\n                    text: \"Connection Error\",\n                    color: \"text-destructive\"\n                };\n            default:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 17\n                    }, this),\n                    text: \"Unknown\",\n                    color: \"text-muted-foreground\"\n                };\n        }\n    };\n    const config = getStatusConfig();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center gap-2 text-sm px-3 py-1.5 rounded-full border shadow-soft\", config.color, config.bgColor, config.borderColor, className),\n        children: [\n            config.icon,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-medium\",\n                children: config.text\n            }, void 0, false, {\n                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_c = ConnectionStatus;\nvar _c;\n$RefreshReg$(_c, \"ConnectionStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat/ConnectionStatus.tsx\n"));

/***/ })

});