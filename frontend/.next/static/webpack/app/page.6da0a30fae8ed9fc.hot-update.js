"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/chat/MessageBubble.tsx":
/*!*******************************************!*\
  !*** ./components/chat/MessageBubble.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageBubble: function() { return /* binding */ MessageBubble; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Info,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* __next_internal_client_entry_do_not_use__ MessageBubble auto */ \n\n\n\n\nfunction MessageBubble(param) {\n    let { message } = param;\n    const isUser = message.type === \"user\";\n    const isSystem = message.type === \"system\";\n    const isError = message.type === \"error\";\n    const getIcon = ()=>{\n        switch(message.type){\n            case \"user\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 16\n                }, this);\n            case \"assistant\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 16\n                }, this);\n            case \"system\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const getBubbleStyles = ()=>{\n        if (isUser) {\n            return \"bg-primary text-primary-foreground ml-auto\";\n        }\n        if (isError) {\n            return \"bg-destructive text-destructive-foreground\";\n        }\n        if (isSystem) {\n            return \"bg-muted text-muted-foreground mx-auto text-center\";\n        }\n        return \"bg-card text-card-foreground border\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex w-full mb-4\", isUser ? \"justify-end\" : \"justify-start\", isSystem ? \"justify-center\" : \"\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"max-w-[80%] min-w-[100px]\", isSystem ? \"max-w-[60%]\" : \"\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg\", getBubbleStyles()),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 mt-0.5\",\n                                    children: getIcon()\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm whitespace-pre-wrap break-words\",\n                                            children: message.content\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 17\n                                        }, this),\n                                        message.sources && message.sources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 pt-3 border-t border-border/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-medium mb-2 text-muted-foreground\",\n                                                    children: \"Sources:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: message.sources.map((source, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs bg-muted/50 rounded p-2 border\",\n                                                            children: [\n                                                                source.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium mb-1\",\n                                                                    children: source.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                                                    lineNumber: 77,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"line-clamp-3 text-muted-foreground\",\n                                                                    children: source.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                                                    lineNumber: 79,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                source.score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs mt-1 text-muted-foreground\",\n                                                                    children: [\n                                                                        \"Relevance: \",\n                                                                        (source.score * 100).toFixed(1),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                                                    lineNumber: 81,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                                            lineNumber: 75,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs mt-3 flex items-center gap-1\", isUser ? \"justify-end text-white/70\" : \"justify-start text-slate-500\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"\\uD83D\\uDD52\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this),\n                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatTimestamp)(message.timestamp)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_c = MessageBubble;\nvar _c;\n$RefreshReg$(_c, \"MessageBubble\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvY2hhdC9NZXNzYWdlQnViYmxlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUV5QjtBQUV3QjtBQUNPO0FBQ0c7QUFNcEQsU0FBU1MsY0FBYyxLQUErQjtRQUEvQixFQUFFQyxPQUFPLEVBQXNCLEdBQS9CO0lBQzVCLE1BQU1DLFNBQVNELFFBQVFFLElBQUksS0FBSztJQUNoQyxNQUFNQyxXQUFXSCxRQUFRRSxJQUFJLEtBQUs7SUFDbEMsTUFBTUUsVUFBVUosUUFBUUUsSUFBSSxLQUFLO0lBRWpDLE1BQU1HLFVBQVU7UUFDZCxPQUFRTCxRQUFRRSxJQUFJO1lBQ2xCLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNQLHFHQUFJQTtvQkFBQ1csV0FBVTs7Ozs7O1lBQ3pCLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNWLHFHQUFHQTtvQkFBQ1UsV0FBVTs7Ozs7O1lBQ3hCLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNULHFHQUFXQTtvQkFBQ1MsV0FBVTs7Ozs7O1lBQ2hDLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNSLHFHQUFJQTtvQkFBQ1EsV0FBVTs7Ozs7O1lBQ3pCO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsTUFBTUMsa0JBQWtCO1FBQ3RCLElBQUlOLFFBQVE7WUFDVixPQUFPO1FBQ1Q7UUFDQSxJQUFJRyxTQUFTO1lBQ1gsT0FBTztRQUNUO1FBQ0EsSUFBSUQsVUFBVTtZQUNaLE9BQU87UUFDVDtRQUNBLE9BQU87SUFDVDtJQUVBLHFCQUNFLDhEQUFDSztRQUFJRixXQUFXZCw4Q0FBRUEsQ0FDaEIsb0JBQ0FTLFNBQVMsZ0JBQWdCLGlCQUN6QkUsV0FBVyxtQkFBbUI7a0JBRTlCLDRFQUFDSztZQUFJRixXQUFXZCw4Q0FBRUEsQ0FDaEIsNkJBQ0FXLFdBQVcsZ0JBQWdCO3NCQUUzQiw0RUFBQ1YscURBQUlBO2dCQUFDYSxXQUFXZCw4Q0FBRUEsQ0FBQyxjQUFjZTswQkFDaEMsNEVBQUNiLDREQUFXQTtvQkFBQ1ksV0FBVTs7c0NBQ3JCLDhEQUFDRTs0QkFBSUYsV0FBVTs7OENBQ2IsOERBQUNFO29DQUFJRixXQUFVOzhDQUNaRDs7Ozs7OzhDQUVILDhEQUFDRztvQ0FBSUYsV0FBVTs7c0RBQ2IsOERBQUNFOzRDQUFJRixXQUFVO3NEQUNaTixRQUFRUyxPQUFPOzs7Ozs7d0NBSWpCVCxRQUFRVSxPQUFPLElBQUlWLFFBQVFVLE9BQU8sQ0FBQ0MsTUFBTSxHQUFHLG1CQUMzQyw4REFBQ0g7NENBQUlGLFdBQVU7OzhEQUNiLDhEQUFDRTtvREFBSUYsV0FBVTs4REFBaUQ7Ozs7Ozs4REFHaEUsOERBQUNFO29EQUFJRixXQUFVOzhEQUNaTixRQUFRVSxPQUFPLENBQUNFLEdBQUcsQ0FBQyxDQUFDQyxRQUFRQyxzQkFDNUIsOERBQUNOOzREQUFnQkYsV0FBVTs7Z0VBQ3hCTyxPQUFPRSxLQUFLLGtCQUNYLDhEQUFDUDtvRUFBSUYsV0FBVTs4RUFBb0JPLE9BQU9FLEtBQUs7Ozs7Ozs4RUFFakQsOERBQUNQO29FQUFJRixXQUFVOzhFQUFzQ08sT0FBT0osT0FBTzs7Ozs7O2dFQUNsRUksT0FBT0csS0FBSyxrQkFDWCw4REFBQ1I7b0VBQUlGLFdBQVU7O3dFQUFxQzt3RUFDckNPLENBQUFBLE9BQU9HLEtBQUssR0FBRyxHQUFFLEVBQUdDLE9BQU8sQ0FBQzt3RUFBRzs7Ozs7Ozs7MkRBUHhDSDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FrQnRCLDhEQUFDTjs0QkFBSUYsV0FBV2QsOENBQUVBLENBQ2hCLHdDQUNBUyxTQUFTLDhCQUE4Qjs7OENBRXZDLDhEQUFDaUI7OENBQUs7Ozs7OztnQ0FDTDNCLDJEQUFlQSxDQUFDUyxRQUFRbUIsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9oRDtLQTVGZ0JwQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL2NoYXQvTWVzc2FnZUJ1YmJsZS50c3g/MDUxYSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgTWVzc2FnZSB9IGZyb20gJ0AvdHlwZXMvY2hhdCdcbmltcG9ydCB7IGZvcm1hdFRpbWVzdGFtcCwgY24gfSBmcm9tICdAL2xpYi91dGlscydcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnXG5pbXBvcnQgeyBVc2VyLCBCb3QsIEFsZXJ0Q2lyY2xlLCBJbmZvIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgTWVzc2FnZUJ1YmJsZVByb3BzIHtcbiAgbWVzc2FnZTogTWVzc2FnZVxufVxuXG5leHBvcnQgZnVuY3Rpb24gTWVzc2FnZUJ1YmJsZSh7IG1lc3NhZ2UgfTogTWVzc2FnZUJ1YmJsZVByb3BzKSB7XG4gIGNvbnN0IGlzVXNlciA9IG1lc3NhZ2UudHlwZSA9PT0gJ3VzZXInXG4gIGNvbnN0IGlzU3lzdGVtID0gbWVzc2FnZS50eXBlID09PSAnc3lzdGVtJ1xuICBjb25zdCBpc0Vycm9yID0gbWVzc2FnZS50eXBlID09PSAnZXJyb3InXG5cbiAgY29uc3QgZ2V0SWNvbiA9ICgpID0+IHtcbiAgICBzd2l0Y2ggKG1lc3NhZ2UudHlwZSkge1xuICAgICAgY2FzZSAndXNlcic6XG4gICAgICAgIHJldHVybiA8VXNlciBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgIGNhc2UgJ2Fzc2lzdGFudCc6XG4gICAgICAgIHJldHVybiA8Qm90IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICByZXR1cm4gPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgY2FzZSAnc3lzdGVtJzpcbiAgICAgICAgcmV0dXJuIDxJbmZvIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIG51bGxcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRCdWJibGVTdHlsZXMgPSAoKSA9PiB7XG4gICAgaWYgKGlzVXNlcikge1xuICAgICAgcmV0dXJuIFwiYmctcHJpbWFyeSB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZCBtbC1hdXRvXCJcbiAgICB9XG4gICAgaWYgKGlzRXJyb3IpIHtcbiAgICAgIHJldHVybiBcImJnLWRlc3RydWN0aXZlIHRleHQtZGVzdHJ1Y3RpdmUtZm9yZWdyb3VuZFwiXG4gICAgfVxuICAgIGlmIChpc1N5c3RlbSkge1xuICAgICAgcmV0dXJuIFwiYmctbXV0ZWQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG14LWF1dG8gdGV4dC1jZW50ZXJcIlxuICAgIH1cbiAgICByZXR1cm4gXCJiZy1jYXJkIHRleHQtY2FyZC1mb3JlZ3JvdW5kIGJvcmRlclwiXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwiZmxleCB3LWZ1bGwgbWItNFwiLFxuICAgICAgaXNVc2VyID8gXCJqdXN0aWZ5LWVuZFwiIDogXCJqdXN0aWZ5LXN0YXJ0XCIsXG4gICAgICBpc1N5c3RlbSA/IFwianVzdGlmeS1jZW50ZXJcIiA6IFwiXCJcbiAgICApfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJtYXgtdy1bODAlXSBtaW4tdy1bMTAwcHhdXCIsXG4gICAgICAgIGlzU3lzdGVtID8gXCJtYXgtdy1bNjAlXVwiIDogXCJcIlxuICAgICAgKX0+XG4gICAgICAgIDxDYXJkIGNsYXNzTmFtZT17Y24oXCJyb3VuZGVkLWxnXCIsIGdldEJ1YmJsZVN0eWxlcygpKX0+XG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtM1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCBtdC0wLjVcIj5cbiAgICAgICAgICAgICAgICB7Z2V0SWNvbigpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB3aGl0ZXNwYWNlLXByZS13cmFwIGJyZWFrLXdvcmRzXCI+XG4gICAgICAgICAgICAgICAgICB7bWVzc2FnZS5jb250ZW50fVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIHsvKiBTb3VyY2VzIHNlY3Rpb24gZm9yIGFzc2lzdGFudCBtZXNzYWdlcyAqL31cbiAgICAgICAgICAgICAgICB7bWVzc2FnZS5zb3VyY2VzICYmIG1lc3NhZ2Uuc291cmNlcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMyBwdC0zIGJvcmRlci10IGJvcmRlci1ib3JkZXIvNTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIG1iLTIgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgU291cmNlczpcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAge21lc3NhZ2Uuc291cmNlcy5tYXAoKHNvdXJjZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwidGV4dC14cyBiZy1tdXRlZC81MCByb3VuZGVkIHAtMiBib3JkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3NvdXJjZS50aXRsZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSBtYi0xXCI+e3NvdXJjZS50aXRsZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsaW5lLWNsYW1wLTMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+e3NvdXJjZS5jb250ZW50fTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7c291cmNlLnNjb3JlICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgbXQtMSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFJlbGV2YW5jZTogeyhzb3VyY2Uuc2NvcmUgKiAxMDApLnRvRml4ZWQoMSl9JVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICBcInRleHQteHMgbXQtMyBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiLFxuICAgICAgICAgICAgICBpc1VzZXIgPyBcImp1c3RpZnktZW5kIHRleHQtd2hpdGUvNzBcIiA6IFwianVzdGlmeS1zdGFydCB0ZXh0LXNsYXRlLTUwMFwiXG4gICAgICAgICAgICApfT5cbiAgICAgICAgICAgICAgPHNwYW4+8J+Vkjwvc3Bhbj5cbiAgICAgICAgICAgICAge2Zvcm1hdFRpbWVzdGFtcChtZXNzYWdlLnRpbWVzdGFtcCl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiZm9ybWF0VGltZXN0YW1wIiwiY24iLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJVc2VyIiwiQm90IiwiQWxlcnRDaXJjbGUiLCJJbmZvIiwiTWVzc2FnZUJ1YmJsZSIsIm1lc3NhZ2UiLCJpc1VzZXIiLCJ0eXBlIiwiaXNTeXN0ZW0iLCJpc0Vycm9yIiwiZ2V0SWNvbiIsImNsYXNzTmFtZSIsImdldEJ1YmJsZVN0eWxlcyIsImRpdiIsImNvbnRlbnQiLCJzb3VyY2VzIiwibGVuZ3RoIiwibWFwIiwic291cmNlIiwiaW5kZXgiLCJ0aXRsZSIsInNjb3JlIiwidG9GaXhlZCIsInNwYW4iLCJ0aW1lc3RhbXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat/MessageBubble.tsx\n"));

/***/ })

});