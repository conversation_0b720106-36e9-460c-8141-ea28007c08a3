"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/chat/ConnectionStatus.tsx":
/*!**********************************************!*\
  !*** ./components/chat/ConnectionStatus.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectionStatus: function() { return /* binding */ ConnectionStatus; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* __next_internal_client_entry_do_not_use__ ConnectionStatus auto */ \n\n\n\nfunction ConnectionStatus(param) {\n    let { status, className } = param;\n    const getStatusConfig = ()=>{\n        switch(status){\n            case \"connecting\":\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 17\n                    }, this),\n                    text: \"Connecting...\",\n                    color: \"text-amber-600\",\n                    bgColor: \"bg-amber-50\",\n                    borderColor: \"border-amber-200\"\n                };\n            case \"connected\":\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 17\n                    }, this),\n                    text: \"Connected\",\n                    color: \"text-green-600\",\n                    bgColor: \"bg-green-50\",\n                    borderColor: \"border-green-200\"\n                };\n            case \"disconnected\":\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 17\n                    }, this),\n                    text: \"Disconnected\",\n                    color: \"text-slate-500\",\n                    bgColor: \"bg-slate-50\",\n                    borderColor: \"border-slate-200\"\n                };\n            case \"error\":\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 17\n                    }, this),\n                    text: \"Connection Error\",\n                    color: \"text-red-600\",\n                    bgColor: \"bg-red-50\",\n                    borderColor: \"border-red-200\"\n                };\n            default:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 17\n                    }, this),\n                    text: \"Unknown\",\n                    color: \"text-slate-500\",\n                    bgColor: \"bg-slate-50\",\n                    borderColor: \"border-slate-200\"\n                };\n        }\n    };\n    const config = getStatusConfig();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center gap-2 text-sm\", config.color, className),\n        children: [\n            config.icon,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: config.text\n            }, void 0, false, {\n                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n_c = ConnectionStatus;\nvar _c;\n$RefreshReg$(_c, \"ConnectionStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat/ConnectionStatus.tsx\n"));

/***/ })

});