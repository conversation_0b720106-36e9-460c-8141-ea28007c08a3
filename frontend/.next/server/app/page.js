/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/home/<USER>/progra/rag-dot/frontend/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/home/<USER>/progra/rag-dot/frontend/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/progra/rag-dot/frontend/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fapp%2Fglobals.css&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fapp%2Fglobals.css&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fapp%2Fpage.tsx&server=true!":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fapp%2Fpage.tsx&server=true! ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRnl6YWF6YWElMkZwcm9ncmElMkZyYWctZG90JTJGZnJvbnRlbmQlMkZhcHAlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yYWctY2hhdC1mcm9udGVuZC8/OGU4NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3l6YWF6YWEvcHJvZ3JhL3JhZy1kb3QvZnJvbnRlbmQvYXBwL3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fapp%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRnl6YWF6YWElMkZwcm9ncmElMkZyYWctZG90JTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZhcHAtcm91dGVyLmpzJm1vZHVsZXM9JTJGaG9tZSUyRnl6YWF6YWElMkZwcm9ncmElMkZyYWctZG90JTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZlcnJvci1ib3VuZGFyeS5qcyZtb2R1bGVzPSUyRmhvbWUlMkZ5emFhemFhJTJGcHJvZ3JhJTJGcmFnLWRvdCUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPSUyRmhvbWUlMkZ5emFhemFhJTJGcHJvZ3JhJTJGcmFnLWRvdCUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbm90LWZvdW5kLWJvdW5kYXJ5LmpzJm1vZHVsZXM9JTJGaG9tZSUyRnl6YWF6YWElMkZwcm9ncmElMkZyYWctZG90JTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJm1vZHVsZXM9JTJGaG9tZSUyRnl6YWF6YWElMkZwcm9ncmElMkZyYWctZG90JTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQWlJO0FBQ2pJLDBPQUFxSTtBQUNySSx3T0FBb0k7QUFDcEksa1BBQXlJO0FBQ3pJLHNRQUFtSjtBQUNuSiIsInNvdXJjZXMiOlsid2VicGFjazovL3JhZy1jaGF0LWZyb250ZW5kLz9hMzZjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUveXphYXphYS9wcm9ncmEvcmFnLWRvdC9mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2FwcC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3l6YWF6YWEvcHJvZ3JhL3JhZy1kb3QvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUveXphYXphYS9wcm9ncmEvcmFnLWRvdC9mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2xheW91dC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3l6YWF6YWEvcHJvZ3JhL3JhZy1kb3QvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3l6YWF6YWEvcHJvZ3JhL3JhZy1kb3QvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9yZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS95emFhemFhL3Byb2dyYS9yYWctZG90L2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvc3RhdGljLWdlbmVyYXRpb24tc2VhcmNocGFyYW1zLWJhaWxvdXQtcHJvdmlkZXIuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_chat_ChatInterface__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/chat/ChatInterface */ \"(ssr)/./components/chat/ChatInterface.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatInterface__WEBPACK_IMPORTED_MODULE_1__.ChatInterface, {}, void 0, false, {\n            fileName: \"/home/<USER>/progra/rag-dot/frontend/app/page.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/app/page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFK0Q7QUFFaEQsU0FBU0M7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUtDLFdBQVU7a0JBQ2QsNEVBQUNILHlFQUFhQTs7Ozs7Ozs7OztBQUdwQiIsInNvdXJjZXMiOlsid2VicGFjazovL3JhZy1jaGF0LWZyb250ZW5kLy4vYXBwL3BhZ2UudHN4Pzc2MDMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IENoYXRJbnRlcmZhY2UgfSBmcm9tICdAL2NvbXBvbmVudHMvY2hhdC9DaGF0SW50ZXJmYWNlJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICByZXR1cm4gKFxuICAgIDxtYWluIGNsYXNzTmFtZT1cImgtc2NyZWVuXCI+XG4gICAgICA8Q2hhdEludGVyZmFjZSAvPlxuICAgIDwvbWFpbj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkNoYXRJbnRlcmZhY2UiLCJIb21lIiwibWFpbiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/chat/ChatInterface.tsx":
/*!*******************************************!*\
  !*** ./components/chat/ChatInterface.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useWebSocket */ \"(ssr)/./hooks/useWebSocket.ts\");\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MessageBubble */ \"(ssr)/./components/chat/MessageBubble.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TypingIndicator */ \"(ssr)/./components/chat/TypingIndicator.tsx\");\n/* harmony import */ var _MessageInput__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MessageInput */ \"(ssr)/./components/chat/MessageInput.tsx\");\n/* harmony import */ var _ConnectionStatus__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ConnectionStatus */ \"(ssr)/./components/chat/ConnectionStatus.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \n\n\n\n\n\n\n\n\n\nfunction ChatInterface() {\n    const { messages, isConnected, isTyping, connectionStatus, sendMessage, clearMessages, connect } = (0,_hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_2__.useWebSocket)();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (messagesEndRef.current) {\n            messagesEndRef.current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    }, [\n        messages,\n        isTyping\n    ]);\n    const handleSendMessage = (message)=>{\n        if (isConnected && message.trim()) {\n            sendMessage(message);\n        }\n    };\n    const handleClearMessages = ()=>{\n        clearMessages();\n    };\n    const handleReconnect = ()=>{\n        connect();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                className: \"rounded-none border-x-0 border-t-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                    className: \"pb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-xl\",\n                                        children: \"UM6P Policies Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground mt-1\",\n                                        children: \"AI-powered assistant for university policies and procedures\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConnectionStatus__WEBPACK_IMPORTED_MODULE_6__.ConnectionStatus, {\n                                        status: connectionStatus\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            connectionStatus === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleReconnect,\n                                                className: \"gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Reconnect\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleClearMessages,\n                                                disabled: messages.length === 0,\n                                                className: \"gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Clear\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: messagesContainerRef,\n                    className: \"h-full overflow-y-auto scrollbar-thin px-4 py-4\",\n                    children: [\n                        messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center max-w-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-4\",\n                                        children: \"\\uD83C\\uDF93\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: \"Welcome to UM6P Policies Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground mb-4\",\n                                        children: \"I'm here to help you with questions about university policies, procedures, and guidelines.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mb-2\",\n                                                children: \"You can ask me about:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"text-left space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Account creation and management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Security policies and procedures\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• IT incident management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Software installation guidelines\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• And much more...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this),\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_3__.MessageBubble, {\n                                message: message\n                            }, message.id, false, {\n                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)),\n                        isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__.TypingIndicator, {}, void 0, false, {\n                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 24\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageInput__WEBPACK_IMPORTED_MODULE_5__.MessageInput, {\n                onSendMessage: handleSendMessage,\n                disabled: !isConnected,\n                isTyping: isTyping\n            }, void 0, false, {\n                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ChatInterface.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/chat/ChatInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./components/chat/ConnectionStatus.tsx":
/*!**********************************************!*\
  !*** ./components/chat/ConnectionStatus.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectionStatus: () => (/* binding */ ConnectionStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* __next_internal_client_entry_do_not_use__ ConnectionStatus auto */ \n\n\n\nfunction ConnectionStatus({ status, className }) {\n    const getStatusConfig = ()=>{\n        switch(status){\n            case \"connecting\":\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 17\n                    }, this),\n                    text: \"Connecting...\",\n                    color: \"text-yellow-500\"\n                };\n            case \"connected\":\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 17\n                    }, this),\n                    text: \"Connected\",\n                    color: \"text-green-500\"\n                };\n            case \"disconnected\":\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 17\n                    }, this),\n                    text: \"Disconnected\",\n                    color: \"text-gray-500\"\n                };\n            case \"error\":\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 17\n                    }, this),\n                    text: \"Connection Error\",\n                    color: \"text-red-500\"\n                };\n            default:\n                return {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 17\n                    }, this),\n                    text: \"Unknown\",\n                    color: \"text-gray-500\"\n                };\n        }\n    };\n    const config = getStatusConfig();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center gap-2 text-sm\", config.color, className),\n        children: [\n            config.icon,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: config.text\n            }, void 0, false, {\n                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/ConnectionStatus.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/chat/ConnectionStatus.tsx\n");

/***/ }),

/***/ "(ssr)/./components/chat/MessageBubble.tsx":
/*!*******************************************!*\
  !*** ./components/chat/MessageBubble.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageBubble: () => (/* binding */ MessageBubble)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Info,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Info,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Info,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bot_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bot,Info,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* __next_internal_client_entry_do_not_use__ MessageBubble auto */ \n\n\n\n\nfunction MessageBubble({ message }) {\n    const isUser = message.type === \"user\";\n    const isSystem = message.type === \"system\";\n    const isError = message.type === \"error\";\n    const getIcon = ()=>{\n        switch(message.type){\n            case \"user\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 16\n                }, this);\n            case \"assistant\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 16\n                }, this);\n            case \"system\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bot_Info_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const getBubbleStyles = ()=>{\n        if (isUser) {\n            return \"bg-primary text-primary-foreground ml-auto\";\n        }\n        if (isError) {\n            return \"bg-destructive text-destructive-foreground\";\n        }\n        if (isSystem) {\n            return \"bg-muted text-muted-foreground mx-auto text-center\";\n        }\n        return \"bg-muted text-muted-foreground\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex w-full mb-4\", isUser ? \"justify-end\" : \"justify-start\", isSystem ? \"justify-center\" : \"\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"max-w-[80%] min-w-[100px]\", isSystem ? \"max-w-[60%]\" : \"\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"shadow-sm\", getBubbleStyles()),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 mt-0.5\",\n                                    children: getIcon()\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm whitespace-pre-wrap break-words\",\n                                            children: message.content\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 17\n                                        }, this),\n                                        message.sources && message.sources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 pt-3 border-t border-primary-foreground/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-medium mb-2 opacity-80\",\n                                                    children: \"Sources:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: message.sources.map((source, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs opacity-70 bg-primary-foreground/10 rounded p-2\",\n                                                            children: [\n                                                                source.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium mb-1\",\n                                                                    children: source.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                                                    lineNumber: 77,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"line-clamp-3\",\n                                                                    children: source.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                                                    lineNumber: 79,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                source.score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs mt-1 opacity-60\",\n                                                                    children: [\n                                                                        \"Relevance: \",\n                                                                        (source.score * 100).toFixed(1),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                                                    lineNumber: 81,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                                            lineNumber: 75,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs mt-2 opacity-60\", isUser ? \"text-right\" : \"text-left\"),\n                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatTimestamp)(message.timestamp)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageBubble.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/chat/MessageBubble.tsx\n");

/***/ }),

/***/ "(ssr)/./components/chat/MessageInput.tsx":
/*!******************************************!*\
  !*** ./components/chat/MessageInput.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageInput: () => (/* binding */ MessageInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MessageInput auto */ \n\n\n\n\n\nfunction MessageInput({ onSendMessage, disabled = false, isTyping = false }) {\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!message.trim() || disabled || isTyping) {\n            return;\n        }\n        onSendMessage(message.trim());\n        setMessage(\"\");\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Focus input when component mounts\n        if (inputRef.current) {\n            inputRef.current.focus();\n        }\n    }, []);\n    const isDisabled = disabled || isTyping || !message.trim();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-t bg-background p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"flex gap-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                        ref: inputRef,\n                        value: message,\n                        onChange: (e)=>setMessage(e.target.value),\n                        onKeyDown: handleKeyDown,\n                        placeholder: disabled ? \"Connecting...\" : isTyping ? \"Assistant is typing...\" : \"Ask me anything about UM6P policies...\",\n                        disabled: disabled,\n                        className: \"resize-none\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageInput.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageInput.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    type: \"submit\",\n                    size: \"icon\",\n                    disabled: isDisabled,\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex-shrink-0\", isDisabled && \"opacity-50 cursor-not-allowed\"),\n                    children: isTyping ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-4 h-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageInput.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageInput.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageInput.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageInput.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/MessageInput.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/chat/MessageInput.tsx\n");

/***/ }),

/***/ "(ssr)/./components/chat/TypingIndicator.tsx":
/*!*********************************************!*\
  !*** ./components/chat/TypingIndicator.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TypingIndicator: () => (/* binding */ TypingIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* __next_internal_client_entry_do_not_use__ TypingIndicator auto */ \n\n\n\nfunction TypingIndicator() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex w-full mb-4 justify-start\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-[80%]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-muted text-muted-foreground shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/TypingIndicator.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: \"Assistant is typing\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/TypingIndicator.tsx\",\n                                        lineNumber: 16,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 ml-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-current rounded-full typing-indicator\",\n                                                style: {\n                                                    animationDelay: \"0ms\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/TypingIndicator.tsx\",\n                                                lineNumber: 18,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-current rounded-full typing-indicator\",\n                                                style: {\n                                                    animationDelay: \"150ms\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/TypingIndicator.tsx\",\n                                                lineNumber: 19,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-current rounded-full typing-indicator\",\n                                                style: {\n                                                    animationDelay: \"300ms\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/TypingIndicator.tsx\",\n                                                lineNumber: 20,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/TypingIndicator.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/TypingIndicator.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/TypingIndicator.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/TypingIndicator.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/TypingIndicator.tsx\",\n                lineNumber: 11,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/TypingIndicator.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/chat/TypingIndicator.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/chat/TypingIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant = \"default\", size = \"default\", ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n            \"bg-primary text-primary-foreground hover:bg-primary/90\": variant === \"default\",\n            \"bg-destructive text-destructive-foreground hover:bg-destructive/90\": variant === \"destructive\",\n            \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\": variant === \"outline\",\n            \"bg-secondary text-secondary-foreground hover:bg-secondary/80\": variant === \"secondary\",\n            \"hover:bg-accent hover:text-accent-foreground\": variant === \"ghost\",\n            \"text-primary underline-offset-4 hover:underline\": variant === \"link\"\n        }, {\n            \"h-10 px-4 py-2\": size === \"default\",\n            \"h-9 rounded-md px-3\": size === \"sm\",\n            \"h-11 rounded-md px-8\": size === \"lg\",\n            \"h-10 w-10\": size === \"icon\"\n        }, className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/ui/button.tsx\",\n        lineNumber: 13,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/ui/card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/ui/card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/ui/card.tsx\",\n        lineNumber: 31,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/ui/card.tsx\",\n        lineNumber: 46,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/ui/card.tsx\",\n        lineNumber: 58,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/ui/card.tsx\",\n        lineNumber: 66,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/components/ui/input.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBQ0U7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGdXQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmFnLWNoYXQtZnJvbnRlbmQvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeD9kYTc5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCBpbnRlcmZhY2UgSW5wdXRQcm9wc1xuICBleHRlbmRzIFJlYWN0LklucHV0SFRNTEF0dHJpYnV0ZXM8SFRNTElucHV0RWxlbWVudD4ge31cblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIElucHV0UHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/useWebSocket.ts":
/*!*******************************!*\
  !*** ./hooks/useWebSocket.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWebSocket: () => (/* binding */ useWebSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ useWebSocket auto */ \n\nconst WEBSOCKET_URL = \"ws://localhost:8000\" || 0;\nfunction useWebSocket() {\n    const [chatState, setChatState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        messages: [],\n        isConnected: false,\n        isTyping: false,\n        connectionStatus: \"disconnected\"\n    });\n    const wsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const clientIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)((0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.generateClientId)());\n    const reconnectTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const reconnectAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const maxReconnectAttempts = 5;\n    const addMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((message)=>{\n        setChatState((prev)=>({\n                ...prev,\n                messages: [\n                    ...prev.messages,\n                    message\n                ]\n            }));\n    }, []);\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (wsRef.current?.readyState === WebSocket.OPEN) {\n            return;\n        }\n        setChatState((prev)=>({\n                ...prev,\n                connectionStatus: \"connecting\"\n            }));\n        try {\n            const ws = new WebSocket(`${WEBSOCKET_URL}/ws/${clientIdRef.current}`);\n            ws.onopen = ()=>{\n                console.log(\"WebSocket connected\");\n                setChatState((prev)=>({\n                        ...prev,\n                        isConnected: true,\n                        connectionStatus: \"connected\"\n                    }));\n                reconnectAttemptsRef.current = 0;\n            };\n            ws.onmessage = (event)=>{\n                try {\n                    const data = JSON.parse(event.data);\n                    switch(data.type){\n                        case \"system\":\n                            addMessage({\n                                id: Date.now().toString(),\n                                type: \"system\",\n                                content: data.message || \"\",\n                                timestamp: data.timestamp\n                            });\n                            break;\n                        case \"rag_complete\":\n                            setChatState((prev)=>({\n                                    ...prev,\n                                    isTyping: false\n                                }));\n                            addMessage({\n                                id: Date.now().toString(),\n                                type: \"assistant\",\n                                content: data.message || \"\",\n                                timestamp: data.timestamp,\n                                sources: data.sources,\n                                metadata: data.metadata,\n                                langwatch_trace_id: data.langwatch_trace_id\n                            });\n                            break;\n                        case \"error\":\n                            setChatState((prev)=>({\n                                    ...prev,\n                                    isTyping: false\n                                }));\n                            addMessage({\n                                id: Date.now().toString(),\n                                type: \"error\",\n                                content: data.message || \"An error occurred\",\n                                timestamp: data.timestamp\n                            });\n                            break;\n                        case \"pong\":\n                            break;\n                        default:\n                            console.log(\"Unknown message type:\", data.type);\n                    }\n                } catch (error) {\n                    console.error(\"Error parsing WebSocket message:\", error);\n                }\n            };\n            ws.onclose = ()=>{\n                console.log(\"WebSocket disconnected\");\n                setChatState((prev)=>({\n                        ...prev,\n                        isConnected: false,\n                        isTyping: false,\n                        connectionStatus: \"disconnected\"\n                    }));\n                // Attempt to reconnect\n                if (reconnectAttemptsRef.current < maxReconnectAttempts) {\n                    reconnectAttemptsRef.current++;\n                    const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);\n                    reconnectTimeoutRef.current = setTimeout(()=>{\n                        console.log(`Attempting to reconnect (${reconnectAttemptsRef.current}/${maxReconnectAttempts})`);\n                        connect();\n                    }, delay);\n                } else {\n                    setChatState((prev)=>({\n                            ...prev,\n                            connectionStatus: \"error\"\n                        }));\n                }\n            };\n            ws.onerror = (error)=>{\n                console.error(\"WebSocket error:\", error);\n                setChatState((prev)=>({\n                        ...prev,\n                        connectionStatus: \"error\"\n                    }));\n            };\n            wsRef.current = ws;\n        } catch (error) {\n            console.error(\"Failed to create WebSocket connection:\", error);\n            setChatState((prev)=>({\n                    ...prev,\n                    connectionStatus: \"error\"\n                }));\n        }\n    }, [\n        addMessage\n    ]);\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (reconnectTimeoutRef.current) {\n            clearTimeout(reconnectTimeoutRef.current);\n        }\n        if (wsRef.current) {\n            wsRef.current.close();\n            wsRef.current = null;\n        }\n        setChatState((prev)=>({\n                ...prev,\n                isConnected: false,\n                connectionStatus: \"disconnected\"\n            }));\n    }, []);\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((message)=>{\n        if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {\n            console.error(\"WebSocket is not connected\");\n            return;\n        }\n        // Add user message to chat\n        addMessage({\n            id: Date.now().toString(),\n            type: \"user\",\n            content: message,\n            timestamp: new Date().toISOString()\n        });\n        // Set typing indicator\n        setChatState((prev)=>({\n                ...prev,\n                isTyping: true\n            }));\n        // Send message to server\n        const payload = {\n            type: \"rag_query\",\n            message: message\n        };\n        wsRef.current.send(JSON.stringify(payload));\n    }, [\n        addMessage\n    ]);\n    const clearMessages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setChatState((prev)=>({\n                ...prev,\n                messages: []\n            }));\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        connect();\n        return ()=>{\n            disconnect();\n        };\n    }, [\n        connect,\n        disconnect\n    ]);\n    return {\n        ...chatState,\n        sendMessage,\n        clearMessages,\n        connect,\n        disconnect\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useWebSocket.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatTimestamp: () => (/* binding */ formatTimestamp),\n/* harmony export */   generateClientId: () => (/* binding */ generateClientId)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction generateClientId() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n}\nfunction formatTimestamp(timestamp) {\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString([], {\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEM7QUFDSjtBQUVqQyxTQUFTRSxHQUFHLEdBQUdDLE1BQW9CO0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEI7QUFFTyxTQUFTQztJQUNkLE9BQU9DLEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLFNBQVMsQ0FBQyxHQUFHLE1BQU1ILEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLFNBQVMsQ0FBQyxHQUFHO0FBQy9GO0FBRU8sU0FBU0MsZ0JBQWdCQyxTQUFpQjtJQUMvQyxNQUFNQyxPQUFPLElBQUlDLEtBQUtGO0lBQ3RCLE9BQU9DLEtBQUtFLGtCQUFrQixDQUFDLEVBQUUsRUFBRTtRQUFFQyxNQUFNO1FBQVdDLFFBQVE7SUFBVTtBQUMxRSIsInNvdXJjZXMiOlsid2VicGFjazovL3JhZy1jaGF0LWZyb250ZW5kLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUNsaWVudElkKCk6IHN0cmluZyB7XG4gIHJldHVybiBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMiwgMTUpICsgTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyaW5nKDIsIDE1KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0VGltZXN0YW1wKHRpbWVzdGFtcDogc3RyaW5nKTogc3RyaW5nIHtcbiAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHRpbWVzdGFtcClcbiAgcmV0dXJuIGRhdGUudG9Mb2NhbGVUaW1lU3RyaW5nKFtdLCB7IGhvdXI6ICcyLWRpZ2l0JywgbWludXRlOiAnMi1kaWdpdCcgfSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIiwiZ2VuZXJhdGVDbGllbnRJZCIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0cmluZyIsImZvcm1hdFRpbWVzdGFtcCIsInRpbWVzdGFtcCIsImRhdGUiLCJEYXRlIiwidG9Mb2NhbGVUaW1lU3RyaW5nIiwiaG91ciIsIm1pbnV0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"23358f7d9755\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yYWctY2hhdC1mcm9udGVuZC8uL2FwcC9nbG9iYWxzLmNzcz83ZjQ0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMjMzNThmN2Q5NzU1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"UM6P Policies Assistant\",\n    description: \"AI-powered assistant for UM6P university policies\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/home/<USER>/progra/rag-dot/frontend/app/layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/progra/rag-dot/frontend/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZnQjtBQUlmLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdULDJKQUFlO3NCQUFHSzs7Ozs7Ozs7Ozs7QUFHekMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yYWctY2hhdC1mcm9udGVuZC8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdVTTZQIFBvbGljaWVzIEFzc2lzdGFudCcsXG4gIGRlc2NyaXB0aW9uOiAnQUktcG93ZXJlZCBhc3Npc3RhbnQgZm9yIFVNNlAgdW5pdmVyc2l0eSBwb2xpY2llcycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/progra/rag-dot/frontend/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fyzaazaa%2Fprogra%2Frag-dot%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();