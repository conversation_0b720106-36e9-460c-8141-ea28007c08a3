import asyncio
import json
import logging
import os
import numpy as np
from typing import Dict, List, Optional
from datetime import datetime

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

from core.ragPipeline import AdvancedRAGPipeline

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def convert_numpy_types(obj):
    """Convert numpy types to Python native types for JSON serialization"""
    if isinstance(obj, np.floating):
        logger.debug(f"Converting numpy float: {obj} ({type(obj)})")
        return float(obj)
    elif isinstance(obj, np.integer):
        logger.debug(f"Converting numpy int: {obj} ({type(obj)})")
        return int(obj)
    elif isinstance(obj, np.ndarray):
        logger.debug(f"Converting numpy array: {obj.shape} ({type(obj)})")
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    elif hasattr(obj, 'dtype') and hasattr(obj, 'item'):
        # Handle numpy scalars that might not be caught by the above
        logger.debug(f"Converting numpy scalar: {obj} ({type(obj)})")
        return obj.item()
    return obj

app = FastAPI(title="RAG WebSocket Server", version="1.0.0")

# Add CORS middleware
allowed_origins = os.getenv("ALLOWED_ORIGINS", "*").split(",")
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ConnectionManager:
    """Manages WebSocket connections"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        
    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"Client {client_id} connected. Total connections: {len(self.active_connections)}")
        
    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"Client {client_id} disconnected. Total connections: {len(self.active_connections)}")
            
    async def send_personal_message(self, message: dict, client_id: str):
        if client_id in self.active_connections:
            try:
                # Convert numpy types to Python types before JSON serialization
                converted_message = convert_numpy_types(message)
                logger.debug(f"Sending message to {client_id}: {type(converted_message)}")
                await self.active_connections[client_id].send_text(json.dumps(converted_message))
            except Exception as e:
                logger.error(f"Error sending message to {client_id}: {e}")
                logger.error(f"Original message: {message}")
                logger.error(f"Converted message: {converted_message}")
                self.disconnect(client_id)

class RAGWebSocketServer:
    """WebSocket server with RAG integration"""
    
    def __init__(self):
        self.manager = ConnectionManager()
        self.rag_pipeline: Optional[AdvancedRAGPipeline] = None
        self.initialize_rag()
        
    def initialize_rag(self):
        """Initialize the RAG pipeline"""
        try:
            # Check for API key
            openai_api_key = os.getenv("OPENAI_API_KEY")
            if not openai_api_key:
                logger.error("OPENAI_API_KEY not found in environment variables")
                return
                
            logger.info("Initializing RAG Pipeline...")
            
            # Initialize RAG pipeline with environment variables (same as main.py)
            # LangWatch will be automatically configured from environment variables
            self.rag_pipeline = AdvancedRAGPipeline(
                persist_dir=os.getenv("RAG_PERSIST_DIR", "./storage"),
                collection_name=os.getenv("RAG_COLLECTION_NAME", "rag_collection"),
                use_reranker=os.getenv("USE_RERANKER", "true").lower() == "true",
                reranker_model=os.getenv("RERANKER_MODEL", "BAAI/bge-reranker-base"),
                reranker_device=os.getenv("RERANKER_DEVICE", "cpu"),
                llm_model=os.getenv("LLM_MODEL", "gpt-4o"),
                embedding_model=os.getenv("EMBEDDING_MODEL", "text-embedding-3-small"),
                chunk_size=int(os.getenv("CHUNK_SIZE", "500")),
                chunk_overlap=int(os.getenv("CHUNK_OVERLAP", "50")),
                reranker_top_n=int(os.getenv("RERANKER_TOP_N", "5"))
            )
            
            # Load or create index
            if not self.rag_pipeline.load_existing_index():
                pdf_directory = os.getenv("RAG_DATA_DIR", "./data")
                if os.path.exists(pdf_directory):
                    logger.info(f"Creating new index from: {pdf_directory}")
                    self.rag_pipeline.create_index_from_pdf_directory(pdf_directory)
                else:
                    logger.error(f"Data directory '{pdf_directory}' not found")
                    return
                    
            # Setup chat engine
            self.rag_pipeline.setup_chat_engine()

            # Log LangWatch status
            if hasattr(self.rag_pipeline, 'get_langwatch_status'):
                langwatch_status = self.rag_pipeline.get_langwatch_status()
                if langwatch_status.get('enabled', False):
                    logger.info(f"LangWatch tracking enabled for WebSocket server")
                    logger.info(f"LangWatch project: {langwatch_status.get('project_name', 'N/A')}")
                else:
                    logger.info("LangWatch tracking disabled or not available")

            logger.info("RAG Pipeline initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing RAG Pipeline: {e}")
            self.rag_pipeline = None
            
    async def handle_rag_query(self, websocket: WebSocket, client_id: str, message: dict):
        """Handle RAG query and stream response"""
        if not self.rag_pipeline:
            await self.manager.send_personal_message({
                "type": "error",
                "message": "RAG system is not available",
                "timestamp": datetime.now().isoformat()
            }, client_id)
            return

        query = message.get("message", "").strip()
        if not query:
            await self.manager.send_personal_message({
                "type": "error",
                "message": "Empty query received",
                "timestamp": datetime.now().isoformat()
            }, client_id)
            return

        try:
            # Process RAG query using the same method as main.py
            # Use the regular chat method (not chat_stream) to get proper LangWatch tracing
            result = self.rag_pipeline.chat(query, verbose=True)

            # Send the complete response (same format as main.py)
            await self.manager.send_personal_message({
                "type": "rag_complete",
                "message": result["response"],
                "sources": result.get("sources", []),
                "metadata": result.get("metadata", {}),
                "langwatch_trace_id": result.get("langwatch_trace_id"),
                "timestamp": datetime.now().isoformat()
            }, client_id)

        except Exception as e:
            logger.error(f"Error processing RAG query: {e}")
            await self.manager.send_personal_message({
                "type": "error",
                "message": f"Error processing your query: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }, client_id)

# Initialize the RAG WebSocket server
rag_server = RAGWebSocketServer()

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    await rag_server.manager.connect(websocket, client_id)
    
    # Send welcome message
    await rag_server.manager.send_personal_message({
        "type": "system",
        "message": "Connected to UM6P Policies Assistant. Ask me anything about university policies!",
        "timestamp": datetime.now().isoformat()
    }, client_id)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                message_type = message.get("type", "message")
                
                if message_type == "rag_query":
                    await rag_server.handle_rag_query(websocket, client_id, message)
                elif message_type == "ping":
                    await rag_server.manager.send_personal_message({
                        "type": "pong",
                        "timestamp": datetime.now().isoformat()
                    }, client_id)
                else:
                    # Echo back for other message types
                    await rag_server.manager.send_personal_message({
                        "type": "echo",
                        "message": message.get("message", ""),
                        "timestamp": datetime.now().isoformat()
                    }, client_id)
                    
            except json.JSONDecodeError:
                await rag_server.manager.send_personal_message({
                    "type": "error",
                    "message": "Invalid JSON format",
                    "timestamp": datetime.now().isoformat()
                }, client_id)
                
    except WebSocketDisconnect:
        rag_server.manager.disconnect(client_id)

@app.get("/health")
async def health_check():
    """Health check endpoint with LangWatch status"""
    rag_status = "available" if rag_server.rag_pipeline else "unavailable"

    # Get LangWatch status if RAG pipeline is available
    langwatch_status = {}
    if rag_server.rag_pipeline and hasattr(rag_server.rag_pipeline, 'get_langwatch_status'):
        langwatch_status = rag_server.rag_pipeline.get_langwatch_status()

    return {
        "status": "healthy",
        "rag_pipeline": rag_status,
        "langwatch": langwatch_status,
        "active_connections": len(rag_server.manager.active_connections),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "RAG WebSocket Server",
        "version": "1.0.0",
        "websocket_endpoint": "/ws/{client_id}",
        "health_check": "/health"
    }

if __name__ == "__main__":
    host = os.getenv("WEBSOCKET_HOST", "0.0.0.0")
    port = int(os.getenv("WEBSOCKET_PORT", "8000"))
    log_level = os.getenv("LOG_LEVEL", "info").lower()

    uvicorn.run(
        "websocket_server:app",
        host=host,
        port=port,
        reload=False,
        log_level=log_level
    )
